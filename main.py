#!/usr/bin/env python3
"""
数据分析主程序

实现完整的数据分析流程：
1. 从数据集加载数据
2. 使用profiling模块进行数据特征抽取和数据集画像报告生成
3. 使用insight模块进行数据洞察生成
4. 将报告保存到report目录

使用示例：
    python main.py
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入数据处理模块
from datasets import Dataset
from operators.core.orchestra import OperatorOrchestratorCore
from operators.core.insight import DataInsightCore
from operators.core.profiling import DatasetProfilerCore

from utils.logger_util import logger


class DataAnalysisPipeline:
    """数据分析流水线"""

    def __init__(self, data_file: str, clean_keys: Optional[List[str]] = None, report_dir: str = "report", result_dir: str = "result"):
        """
        初始化数据分析流水线

        Args:
            data_file: 数据文件路径
            clean_keys: 需要分析的字段列表，如果为None则自动检测
            report_dir: 报告输出目录
            result_dir: 结果输出目录
        """
        self.data_file = data_file
        self.clean_keys = clean_keys  # 可能为None，在load_dataset中自动检测
        self.report_dir = Path(report_dir)
        self.result_dir = Path(result_dir)

        # 确保目录存在
        self.report_dir.mkdir(exist_ok=True)
        self.result_dir.mkdir(exist_ok=True)

        # 设置报告文件路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.profile_report_path = self.report_dir / f"data_profile_report_{timestamp}.json"
        self.insight_report_path = self.report_dir / f"data_insight_report_{timestamp}.json"
        # 算子编排结果现在整合到洞察报告中，使用相同的文件路径
        self.enhanced_insight_report_path = self.report_dir / f"data_insight_report_with_orchestration_{timestamp}.json"

    def _detect_string_fields(self, dataset: Dataset) -> List[str]:
        """
        自动检测数据集中的文本字段

        使用预定义的字段名称列表进行匹配，不考虑数据类型映射和优先级排序。
        如果数据集的字段名在预定义列表中或包含预定义名称，则认为是文本字段。

        Args:
            dataset: 数据集

        Returns:
            匹配到的文本字段列表
        """
        # 预定义的可能字段名称列表
        candidate_field_names = [
            # 对话相关字段
            'user_message', 'assistant_message', 'context', 'conversation', 
            'conversations', 'human', 'assistant', 'user', 'bot', 'system',

            # 文本内容字段
            'text', 'content', 'title', 'description', 'summary',
            'body', 'article', 'paragraph', 'sentence',

            # 问答相关字段
            'question', 'answer', 'query', 'response', 'reply',
            'input', 'output', 'prompt', 'completion',

            # 聊天消息字段
            'message', 'msg', 'chat', 'dialogue',

            # 文档相关字段
            'document', 'doc', 'page', 'section',

            # 其他常见文本字段
            'instruction', 'task', 'example', 'sample',
            'story', 'narrative', 'comment', 'review',
            'note', 'remark', 'explanation', 'detail'
        ]

        # 获取数据集中的所有字段
        dataset_fields = list(dataset.features.keys())

        # 匹配字段名称
        matched_fields = []
        for field_name in dataset_fields:
            # 检查字段名是否在候选列表中（不区分大小写）
            if field_name.lower() in [name.lower() for name in candidate_field_names]:
                matched_fields.append(field_name)
                continue

            # 检查字段名是否包含候选名称（部分匹配）
            for candidate in candidate_field_names:
                if candidate.lower() in field_name.lower():
                    matched_fields.append(field_name)
                    break

        return matched_fields

    def load_dataset(self) -> Dataset:
        """
        从JSONL文件加载数据集

        Returns:
            加载的数据集
        """
        logger.info(f"[main]: 正在加载数据集: {self.data_file}")

        if not os.path.exists(self.data_file):
            raise FileNotFoundError(f"数据文件不存在: {self.data_file}")

        # 读取JSONL文件
        data_list = []
        with open(self.data_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                try:
                    data = json.loads(line)
                    data_list.append(data)
                except json.JSONDecodeError as e:
                    logger.warning(f"[main]: 警告: 第{line_num}行JSON解析失败: {e}")
                    continue

        if not data_list:
            raise ValueError("数据文件为空或无有效数据")

        # 创建Dataset对象
        dataset = Dataset.from_list(data_list)
        logger.info(f"[main]: 成功加载数据集，共 {len(dataset)} 个样本")

        # 打印数据集基本信息
        logger.info(f"[main]: 数据集字段: {list(dataset.features.keys())}")

        # 如果没有指定clean_keys，自动检测字符串字段
        if self.clean_keys is None:
            logger.info("[main]: 未指定分析字段，正在自动检测字符串类型字段...")
            self.clean_keys = self._detect_string_fields(dataset)
            logger.info(f"[main]: 自动检测到的字符串字段: {self.clean_keys}")

        logger.info(f"[main]: 分析字段: {self.clean_keys}")

        # 验证分析字段是否存在
        if self.clean_keys:
            missing_fields = [field for field in self.clean_keys if field not in dataset.features]
            if missing_fields:
                logger.warning(f"[main]: 警告: 以下字段在数据集中不存在: {missing_fields}")
                # 过滤掉不存在的字段
                self.clean_keys = [field for field in self.clean_keys if field in dataset.features]
                logger.info(f"[main]: 实际分析字段: {self.clean_keys}")

        if not self.clean_keys:
            raise ValueError("没有找到可分析的字符串字段")

        return dataset

    def generate_profile_report(self, dataset: Dataset) -> Dict[str, Any]:
        """
        生成数据画像报告

        Args:
            dataset: 数据集

        Returns:
            数据画像报告
        """
        logger.info("[main]: " + "="*50)
        logger.info("[main]: 步骤1: 生成数据画像报告")
        logger.info("[main]: " + "="*50)

        if not self.clean_keys:
            raise ValueError("没有可分析的字段")

        try:
            profiler_core = DatasetProfilerCore()
            # 生成数据画像报告
            profile_report = generate_data_profile_report(
                dataset=dataset,
                clean_keys=self.clean_keys,
                output_file=str(self.profile_report_path)
            )

            logger.info(f"[main]: 数据画像报告生成完成")
            logger.info(f"[main]: 报告保存路径: {self.profile_report_path}")

            # 打印报告摘要
            self._print_profile_summary(profile_report)

            return profile_report

        except Exception as e:
            logger.error(f"[main]: 数据画像报告生成失败: {e}")
            raise

    def generate_insight_report(self, profile_report: Dict[str, Any], dataset: Dataset) -> Dict[str, Any]:
        """
        生成数据洞察报告

        Args:
            profile_report: 数据画像报告
            dataset: 原始数据集

        Returns:
            数据洞察报告
        """
        logger.info("[main]: " + "="*50)
        logger.info("[main]: 步骤2: 生成数据洞察报告")
        logger.info("[main]: " + "="*50)

        try:
            # 生成数据洞察报告
            insight_report = generate_insight_report(
                profile_report=profile_report,
                output_file=str(self.insight_report_path)
            )

            logger.info(f"[main]: 数据洞察报告生成完成")
            logger.info(f"[main]: 报告保存路径: {self.insight_report_path}")

            # 打印报告摘要
            self._print_insight_summary(insight_report)

            return insight_report

        except Exception as e:
            logger.error(f"[main]: 数据洞察报告生成失败: {e}")
            raise

    def generate_cleaning_orchestration(self, insight_report: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成清洗算子编排并整合到洞察报告中

        Args:
            insight_report: 数据洞察报告

        Returns:
            包含算子编排的增强洞察报告
        """
        logger.info("[main]: " + "="*50)
        logger.info("[main]: 步骤3: 生成清洗算子编排")
        logger.info("[main]: " + "="*50)

        try:
            # 生成清洗算子编排并整合到洞察报告中
            enhanced_insight_report = orchestrate_cleaning_operators(
                insight_report=insight_report,
                output_file=str(self.enhanced_insight_report_path)
            )

            logger.info(f"[main]: 清洗算子编排生成完成并已整合到洞察报告中")
            logger.info(f"[main]: 增强洞察报告保存路径: {self.enhanced_insight_report_path}")

            # 打印编排摘要
            self._print_cleaning_summary(enhanced_insight_report)

            return enhanced_insight_report

        except Exception as e:
            logger.error(f"[main]: 清洗算子编排生成失败: {e}")
            raise

    def _print_profile_summary(self, profile_report: Dict[str, Any]):
        """打印数据画像报告摘要"""
        summary = profile_report.get('summary', {})

        logger.info(f"[main]: 数据画像报告摘要:")
        logger.info(f"[main]: 总样本数: {summary.get('total_samples', 0)}")
        logger.info(f"[main]: 分析字段: {summary.get('analyzed_fields', [])}")
        logger.info(f"[main]: 平均文本长度: {summary.get('avg_text_length', 0):.2f} 字符")
        logger.info(f"[main]: 内容多样性分数: {summary.get('content_diversity', 0):.2f}")

        # 语言分布
        lang_dist = summary.get('language_distribution', {}).get('proportions', {})
        if lang_dist:
            logger.info(f"[main]: 语言分布:")
            for lang, stats in lang_dist.items():
                logger.info(f"[main]:   {lang}: {stats.get('count', 0)} 样本 ({stats.get('proportion', 0)*100:.1f}%)")

    def _print_insight_summary(self, insight_report: Dict[str, Any]):
        """打印数据洞察报告摘要"""
        metadata = insight_report.get('metadata', {})
        quality_score = insight_report.get('quality_score', 0)
        comprehensive_insights = insight_report.get('comprehensive_insights', {})

        logger.info(f"[main]: 数据洞察报告摘要:")
        logger.info(f"[main]: 分析时间: {metadata.get('analysis_timestamp', 'N/A')}")
        logger.info(f"[main]: 总样本数: {metadata.get('total_samples', 0)}")

        # 质量评分
        if isinstance(quality_score, (int, float)) and quality_score > 0:
            logger.info(f"[main]: 数据质量评分: {quality_score:.2f}/1.0")
            # 根据评分确定质量等级
            if quality_score >= 0.8:
                quality_level = "优秀"
            elif quality_score >= 0.6:
                quality_level = "良好"
            elif quality_score >= 0.4:
                quality_level = "一般"
            else:
                quality_level = "较差"
            logger.info(f"[main]: 质量等级: {quality_level}")

        # 综合洞察摘要
        if comprehensive_insights:
            issue_dist = comprehensive_insights.get('issue_distribution', {})
            logger.info(f"[main]: 发现问题: 高 {issue_dist.get('high', 0)} 个, 中 {issue_dist.get('medium', 0)} 个, 低 {issue_dist.get('low', 0)} 个")

        # 领域分类
        domain_info = insight_report.get('domain_classification', '')
        if domain_info:
            logger.info(f"[main]: 领域分类: {domain_info[:100]}...")

    def _print_cleaning_summary(self, enhanced_insight_report):
        """打印清洗算子编排摘要"""
        cleaning_orchestration = enhanced_insight_report.get('cleaning_orchestration', {})
        operators = cleaning_orchestration.get('operators', [])

        # 显示前3个编排算子
        logger.info(f"[main]: 编排算子数量: {len(operators)}")
        for i, op in enumerate(operators[:3], 1):
            name = op.get('name', '未知算子')
            clean_keys = op.get('clean_keys', [])
            reason = op.get('reason', '无编排理由')
            logger.info(f"[main]: {i}. {name}")
            logger.info(f"[main]: 处理字段: {', '.join(clean_keys) if clean_keys else '未指定'}")
            logger.info(f"[main]: 理由: {reason[:80]}...")

    def run_analysis(self) -> Dict[str, Any]:
        """
        运行完整的数据分析流程

        Returns:
            包含所有报告的结果字典
        """
        logger.info("[main]: 🚀 开始数据分析流程")
        logger.info(f"[main]: 📁 数据文件: {self.data_file}")
        logger.info(f"[main]: 📋 分析字段: {self.clean_keys}")
        logger.info(f"[main]: 📂 报告目录: {self.report_dir}")

        try:
            # 1. 加载数据集
            dataset = self.load_dataset()

            # 2. 生成数据画像报告
            profile_report = self.generate_profile_report(dataset)

            # 3. 生成数据洞察报告
            insight_report = self.generate_insight_report(profile_report, dataset)

            # 4. 生成清洗算子编排并整合到洞察报告中
            enhanced_insight_report = self.generate_cleaning_orchestration(insight_report)

            logger.info("[main]: " + "="*50)
            logger.info("[main]: 数据分析流程完成!")
            logger.info("[main]: " + "="*50)
            logger.info(f"[main]: 数据画像报告: {self.profile_report_path}")
            logger.info(f"[main]: 数据洞察报告: {self.insight_report_path}")
            logger.info(f"[main]: 增强洞察报告（含算子编排）: {self.enhanced_insight_report_path}")

            return {
                'profile_report': profile_report,
                'insight_report': insight_report,
                'enhanced_insight_report': enhanced_insight_report,
                'profile_report_path': str(self.profile_report_path),
                'insight_report_path': str(self.insight_report_path),
                'enhanced_insight_report_path': str(self.enhanced_insight_report_path)
            }

        except Exception as e:
            logger.error(f"[main]: 数据分析流程失败: {e}")
            raise


def main():
    """主函数"""
    # 配置参数
    data_file = "data/test/test_data_fine_tuning_50_samples.jsonl"
    # data_file = "/matrix/0-Work/0_dev/fin_data_synthesis/data/processed/fin-r1/FinanceIQ/FinanceIQ_default_validation_50.jsonl"

    # 不预设clean_keys，让程序自动检测字符串类型字段
    # 程序会自动识别对话数据中的文本字段，如: user_message, assistant_message, context等

    # 创建并运行分析流水线
    pipeline = DataAnalysisPipeline(
        data_file=data_file,
        clean_keys=None,  # 自动检测
        report_dir="report",
        result_dir="result"
    )

    try:
        results = pipeline.run_analysis()
        return results
    except Exception as e:
        logger.error(f"[main]: 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()